{"remainingRequest": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\src\\bysc_system\\views\\terminalAssignment\\index.vue", "mtime": 1753855459697}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1745221307620}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745221302709}, {"path": "D:\\bw\\idcardbox-vue\\bysc-vue-system\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745221307761}], "contextDependencies": [], "result": ["\r\nimport Vue from 'vue';\r\nimport Grid from '@/components/Grid';\r\nimport LoadingFix from '@/utils/loading-fix';\r\nimport AssignTenantDialog from './components/AssignTenantDialog.vue';\r\nimport ChangeTenantDialog from './components/ChangeTenantDialog.vue';\r\n\r\nconst defaultSearchForm = {\r\n  icbId: '',\r\n  ipAddress: '',\r\n  icbName: '',\r\n  status: '',\r\n  distributeStatus: ''\r\n};\r\n\r\nexport default {\r\n  components: {\r\n    Grid,\r\n    AssignTenantDialog,\r\n    ChangeTenantDialog\r\n  },\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n\r\n    // 清理遮罩层观察器\r\n    if (this.maskObserver) {\r\n      this.maskObserver.disconnect();\r\n    }\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    return {\r\n      submitLoading: false,\r\n      tableLoading: false,\r\n      searchForm: Object.assign({}, defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: '证件柜ID',\r\n          key: 'icbId',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: 'IP地址',\r\n          key: 'ipAddress',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '证件柜名称',\r\n          key: 'icbName',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '上线时间',\r\n          key: 'onlineTime',\r\n          tooltip: true,\r\n          minWidth: 180,\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '分配状态',\r\n          key: 'distributeStatus',\r\n          slot: 'distributeStatus',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '当前租户',\r\n          key: 'currentTenant',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n      ],\r\n      tableData: [],\r\n      // 弹窗相关\r\n      assignDialogVisible: false,\r\n      changeDialogVisible: false,\r\n      tenantList: [], // 租户列表\r\n      currentTerminal: {} // 当前操作的终端\r\n    };\r\n  },\r\n  mounted() {\r\n    // 确保页面加载时重置所有loading状态\r\n    this.tableLoading = false;\r\n\r\n    // 使用 LoadingFix 工具清理遮罩层\r\n    // this.$nextTick(() => {\r\n    //   setTimeout(() => {\r\n    //     LoadingFix.forceClearMasks();\r\n    //     this.tableLoading = false;\r\n    //     console.log('已使用 LoadingFix 重置页面loading状态');\r\n    //   }, 100);\r\n    // });\r\n\r\n    // // 启动自动清理监听\r\n    // this.maskObserver = LoadingFix.startAutoCleanup();\r\n\r\n    // 添加测试数据\r\n    this.loadTestData();\r\n  },\r\n\r\n  methods: {\r\n    // 获取数据时的回调\r\n    getDatas(data) {\r\n      this.tableData = data;\r\n      this.tableLoading = false; // 确保数据加载完成后隐藏loading\r\n    },\r\n\r\n    // 分配租户\r\n    handleAssignTenant(row) {\r\n      this.currentTerminal = Object.assign({}, row);\r\n      this.assignDialogVisible = true;\r\n    },\r\n\r\n    // 更改租户\r\n    handleChangeTenant(row) {\r\n      this.currentTerminal = Object.assign({}, row);\r\n      this.changeDialogVisible = true;\r\n    },\r\n\r\n    // 分配租户确认\r\n    handleAssignConfirm(data) {\r\n      this.submitLoading = true;\r\n\r\n      // 模拟API调用\r\n      // setTimeout(() => {\r\n      //   // 更新测试数据\r\n      //   const terminal = this.tableData.find(item => item.icbId === data.icbId);\r\n      //   if (terminal) {\r\n      //     const tenant = this.tenantList.find(t => t.id === data.tenantId);\r\n      //     terminal.distributeStatus = '1';\r\n      //     terminal.currentTenant = tenant ? tenant.tenantName : '';\r\n      //     terminal.distributeTenantId = data.tenantId;\r\n      //   }\r\n\r\n      //   this.$message({\r\n      //     message: '分配成功',\r\n      //     type: 'success'\r\n      //   });\r\n      //   this.assignDialogVisible = false;\r\n      //   this.submitLoading = false;\r\n      // }, 1000);\r\n    },\r\n\r\n    // 更改租户确认\r\n    handleChangeConfirm(data) {\r\n      this.submitLoading = true;\r\n\r\n      // 模拟API调用\r\n      setTimeout(() => {\r\n        // 更新测试数据\r\n        const terminal = this.tableData.find(item => item.icbId === data.icbId);\r\n        if (terminal) {\r\n          const tenant = this.tenantList.find(t => t.id === data.tenantId);\r\n          terminal.currentTenant = tenant ? tenant.tenantName : '';\r\n          terminal.distributeTenantId = data.tenantId;\r\n        }\r\n\r\n        this.$message({\r\n          message: '更改成功',\r\n          type: 'success'\r\n        });\r\n        this.changeDialogVisible = false;\r\n        this.submitLoading = false;\r\n      }, 1000);\r\n    },\r\n\r\n    // 分配弹窗关闭\r\n    handleAssignClose() {\r\n      this.currentTerminal = {};\r\n    },\r\n\r\n    // 更改弹窗关闭\r\n    handleChangeClose() {\r\n      this.currentTerminal = {};\r\n    },\r\n\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n\r\n    resetTable() {\r\n      this.searchForm = Object.assign({}, defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n\r\n    getColumn(e) {\r\n      this.columns = e;\r\n    },\r\n\r\n    // 加载测试数据\r\n    loadTestData() {\r\n      this.tableData = [\r\n        {\r\n          id: 1,\r\n          icbId: 'T0001',\r\n          ipAddress: '***********',\r\n          icbName: '测试柜1',\r\n          onlineTime: '2025-07-29',\r\n          status: 'online',\r\n          distributeStatus: '1',\r\n          currentTenant: '租户A',\r\n          distributeTenantId: 1\r\n        },\r\n        {\r\n          id: 2,\r\n          icbId: 'T0002',\r\n          ipAddress: '***********',\r\n          icbName: '测试柜2',\r\n          onlineTime: '2025-07-29',\r\n          status: 'online',\r\n          distributeStatus: '0',\r\n          currentTenant: '',\r\n          distributeTenantId: null\r\n        }\r\n      ];\r\n\r\n      // 模拟租户列表数据\r\n      this.tenantList = [\r\n        {id: 1, tenantName: '租户A'},\r\n        {id: 2, tenantName: '租户B'},\r\n        {id: 3, tenantName: '租户C'}\r\n      ];\r\n    }\r\n  },\r\n};\r\n", {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAmLA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/bysc_system/views/terminalAssignment", "sourcesContent": ["\r\n<template>\r\n  <div>\r\n    <el-row>\r\n      <el-col :span=\"24\">\r\n        <Grid\r\n          api=\"terminal/terminal-page\"\r\n          :event-bus=\"searchEventBus\"\r\n          :search-params=\"searchForm\"\r\n          :newcolumn=\"columns\"\r\n          @datas=\"getDatas\"\r\n          @columnChange=\"getColumn\"\r\n          :auto-load=\"false\"\r\n          ref=\"grid\"\r\n        >\r\n          <div slot=\"search\">\r\n            <el-form\r\n              :inline=\"true\"\r\n              :model=\"searchForm\"\r\n              class=\"demo-form-inline\"\r\n            >\r\n              <el-form-item label=\"证件柜ID\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.icbId\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入证件柜ID\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"IP地址\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.ipAddress\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入IP地址\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"证件柜名称\">\r\n                <el-input\r\n                  style=\"width: 200px;margin:0 10px 0 0;\"\r\n                  v-model.trim=\"searchForm.icbName\"\r\n                  size=\"small\"\r\n                  placeholder=\"请输入证件柜名称\"\r\n                ></el-input>\r\n              </el-form-item>\r\n              <el-form-item label=\"状态\">\r\n                <el-select\r\n                  size=\"small\"\r\n                  clearable\r\n                  @keydown.enter.native.prevent=\"searchTable\"\r\n                  v-model=\"searchForm.status\"\r\n                  placeholder=\"请选择状态\"\r\n                >\r\n                  <el-option label=\"在线\" value=\"1\"></el-option>\r\n                  <el-option label=\"离线\" value=\"0\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item label=\"分配状态\">\r\n                <el-select\r\n                  size=\"small\"\r\n                  clearable\r\n                  @keydown.enter.native.prevent=\"searchTable\"\r\n                  v-model=\"searchForm.distributeStatus\"\r\n                  placeholder=\"请选择分配状态\"\r\n                >\r\n                  <el-option label=\"已分配\" value=\"1\"></el-option>\r\n                  <el-option label=\"待分配\" value=\"0\"></el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item>\r\n                <el-button\r\n                  size=\"small\"\r\n                  type=\"primary\"\r\n                  style=\"margin: 0 0 0 10px\"\r\n                  @click=\"searchTable\"\r\n                  >搜索</el-button>\r\n                <el-button size=\"small\" @click=\"resetTable\">重置</el-button>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n          <el-table slot=\"table\" slot-scope=\"{}\" v-loading=\"tableLoading\" ref=\"multipleTable\" :data=\"tableData\" stripe style=\"width: 100%\">\r\n            <el-table-column fixed=\"left\" :align=\"'center'\" label=\"序号\" type=\"index\" width=\"50\">\r\n            </el-table-column>\r\n            <template v-for=\"(item, index) in columns\">\r\n              <el-table-column\r\n                v-if=\"item.slot === 'status'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"100\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"scope.row.status === '1' ? 'success' : 'danger'\">\r\n                    {{ scope.row.status === '1' ? '在线' : '离线' }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else-if=\"item.slot === 'distributeStatus'\"\r\n                :show-overflow-tooltip=\"true\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n                :key=\"index\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                min-width=\"100\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-tag :type=\"scope.row.distributeStatus === 'assigned' ? 'success' : 'warning'\">\r\n                    {{ scope.row.distributeStatus === '1' ? '已分配' : '待分配' }}\r\n                  </el-tag>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                v-else\r\n                :show-overflow-tooltip=\"true\"\r\n                :key=\"item.key\"\r\n                :prop=\"item.key\"\r\n                :label=\"item.title\"\r\n                :min-width=\"item.width ? item.width : '150'\"\r\n                :align=\"item.align ? item.align : 'center'\"\r\n              >\r\n              </el-table-column>\r\n            </template>\r\n            <el-table-column\r\n              fixed=\"right\"\r\n              align=\"center\"\r\n              label=\"操作\"\r\n              type=\"action\"\r\n              width=\"200\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-if=\"scope.row.distributeStatus === '1'\"\r\n                  style=\"margin-right:6px\"\r\n                  @click=\"handleChangeTenant(scope.row)\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  v-permission=\"'terminal_admin_change'\"\r\n                  >更改租户</el-button>\r\n                <el-button\r\n                  v-if=\"scope.row.distributeStatus === '0'\"\r\n                  style=\"margin-right:6px\"\r\n                  @click=\"handleAssignTenant(scope.row)\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  v-permission=\"'terminal_admin_assign'\"\r\n                  >分配租户</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </Grid>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 分配租户弹窗 -->\r\n    <AssignTenantDialog\r\n      :visible.sync=\"assignDialogVisible\"\r\n      :terminal-info=\"currentTerminal\"\r\n      :tenant-list=\"tenantList\"\r\n      :loading=\"submitLoading\"\r\n      @confirm=\"handleAssignConfirm\"\r\n      @close=\"handleAssignClose\"\r\n    />\r\n\r\n    <!-- 更改租户弹窗 -->\r\n    <ChangeTenantDialog\r\n      :visible.sync=\"changeDialogVisible\"\r\n      :terminal-info=\"currentTerminal\"\r\n      :tenant-list=\"tenantList\"\r\n      :loading=\"submitLoading\"\r\n      @confirm=\"handleChangeConfirm\"\r\n      @close=\"handleChangeClose\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Vue from 'vue';\r\nimport Grid from '@/components/Grid';\r\nimport LoadingFix from '@/utils/loading-fix';\r\nimport AssignTenantDialog from './components/AssignTenantDialog.vue';\r\nimport ChangeTenantDialog from './components/ChangeTenantDialog.vue';\r\n\r\nconst defaultSearchForm = {\r\n  icbId: '',\r\n  ipAddress: '',\r\n  icbName: '',\r\n  status: '',\r\n  distributeStatus: ''\r\n};\r\n\r\nexport default {\r\n  components: {\r\n    Grid,\r\n    AssignTenantDialog,\r\n    ChangeTenantDialog\r\n  },\r\n  destroyed() {\r\n    this.searchEventBus.$off();\r\n\r\n    // 清理遮罩层观察器\r\n    if (this.maskObserver) {\r\n      this.maskObserver.disconnect();\r\n    }\r\n  },\r\n  data() {\r\n    this.searchEventBus = new Vue();\r\n    return {\r\n      submitLoading: false,\r\n      tableLoading: false,\r\n      searchForm: Object.assign({}, defaultSearchForm),\r\n      columns: [\r\n        {\r\n          title: '证件柜ID',\r\n          key: 'icbId',\r\n          tooltip: true,\r\n          minWidth: 120,\r\n        },\r\n        {\r\n          title: 'IP地址',\r\n          key: 'ipAddress',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '证件柜名称',\r\n          key: 'icbName',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n        {\r\n          title: '上线时间',\r\n          key: 'onlineTime',\r\n          tooltip: true,\r\n          minWidth: 180,\r\n        },\r\n        {\r\n          title: '状态',\r\n          key: 'status',\r\n          slot: 'status',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '分配状态',\r\n          key: 'distributeStatus',\r\n          slot: 'distributeStatus',\r\n          tooltip: true,\r\n          minWidth: 100,\r\n        },\r\n        {\r\n          title: '当前租户',\r\n          key: 'currentTenant',\r\n          tooltip: true,\r\n          minWidth: 150,\r\n        },\r\n      ],\r\n      tableData: [],\r\n      // 弹窗相关\r\n      assignDialogVisible: false,\r\n      changeDialogVisible: false,\r\n      tenantList: [], // 租户列表\r\n      currentTerminal: {} // 当前操作的终端\r\n    };\r\n  },\r\n  mounted() {\r\n    // 确保页面加载时重置所有loading状态\r\n    this.tableLoading = false;\r\n\r\n    // 使用 LoadingFix 工具清理遮罩层\r\n    // this.$nextTick(() => {\r\n    //   setTimeout(() => {\r\n    //     LoadingFix.forceClearMasks();\r\n    //     this.tableLoading = false;\r\n    //     console.log('已使用 LoadingFix 重置页面loading状态');\r\n    //   }, 100);\r\n    // });\r\n\r\n    // // 启动自动清理监听\r\n    // this.maskObserver = LoadingFix.startAutoCleanup();\r\n\r\n    // 添加测试数据\r\n    this.loadTestData();\r\n  },\r\n\r\n  methods: {\r\n    // 获取数据时的回调\r\n    getDatas(data) {\r\n      this.tableData = data;\r\n      this.tableLoading = false; // 确保数据加载完成后隐藏loading\r\n    },\r\n\r\n    // 分配租户\r\n    handleAssignTenant(row) {\r\n      this.currentTerminal = Object.assign({}, row);\r\n      this.assignDialogVisible = true;\r\n    },\r\n\r\n    // 更改租户\r\n    handleChangeTenant(row) {\r\n      this.currentTerminal = Object.assign({}, row);\r\n      this.changeDialogVisible = true;\r\n    },\r\n\r\n    // 分配租户确认\r\n    handleAssignConfirm(data) {\r\n      this.submitLoading = true;\r\n\r\n      // 模拟API调用\r\n      // setTimeout(() => {\r\n      //   // 更新测试数据\r\n      //   const terminal = this.tableData.find(item => item.icbId === data.icbId);\r\n      //   if (terminal) {\r\n      //     const tenant = this.tenantList.find(t => t.id === data.tenantId);\r\n      //     terminal.distributeStatus = '1';\r\n      //     terminal.currentTenant = tenant ? tenant.tenantName : '';\r\n      //     terminal.distributeTenantId = data.tenantId;\r\n      //   }\r\n\r\n      //   this.$message({\r\n      //     message: '分配成功',\r\n      //     type: 'success'\r\n      //   });\r\n      //   this.assignDialogVisible = false;\r\n      //   this.submitLoading = false;\r\n      // }, 1000);\r\n    },\r\n\r\n    // 更改租户确认\r\n    handleChangeConfirm(data) {\r\n      this.submitLoading = true;\r\n\r\n      // 模拟API调用\r\n      setTimeout(() => {\r\n        // 更新测试数据\r\n        const terminal = this.tableData.find(item => item.icbId === data.icbId);\r\n        if (terminal) {\r\n          const tenant = this.tenantList.find(t => t.id === data.tenantId);\r\n          terminal.currentTenant = tenant ? tenant.tenantName : '';\r\n          terminal.distributeTenantId = data.tenantId;\r\n        }\r\n\r\n        this.$message({\r\n          message: '更改成功',\r\n          type: 'success'\r\n        });\r\n        this.changeDialogVisible = false;\r\n        this.submitLoading = false;\r\n      }, 1000);\r\n    },\r\n\r\n    // 分配弹窗关闭\r\n    handleAssignClose() {\r\n      this.currentTerminal = {};\r\n    },\r\n\r\n    // 更改弹窗关闭\r\n    handleChangeClose() {\r\n      this.currentTerminal = {};\r\n    },\r\n\r\n    searchTable() {\r\n      this.$refs.grid.query();\r\n    },\r\n\r\n    resetTable() {\r\n      this.searchForm = Object.assign({}, defaultSearchForm);\r\n      this.$nextTick(() => {\r\n        this.$refs.grid.query();\r\n      });\r\n    },\r\n\r\n    getColumn(e) {\r\n      this.columns = e;\r\n    },\r\n\r\n    // 加载测试数据\r\n    loadTestData() {\r\n      this.tableData = [\r\n        {\r\n          id: 1,\r\n          icbId: 'T0001',\r\n          ipAddress: '***********',\r\n          icbName: '测试柜1',\r\n          onlineTime: '2025-07-29',\r\n          status: 'online',\r\n          distributeStatus: '1',\r\n          currentTenant: '租户A',\r\n          distributeTenantId: 1\r\n        },\r\n        {\r\n          id: 2,\r\n          icbId: 'T0002',\r\n          ipAddress: '***********',\r\n          icbName: '测试柜2',\r\n          onlineTime: '2025-07-29',\r\n          status: 'online',\r\n          distributeStatus: '0',\r\n          currentTenant: '',\r\n          distributeTenantId: null\r\n        }\r\n      ];\r\n\r\n      // 模拟租户列表数据\r\n      this.tenantList = [\r\n        {id: 1, tenantName: '租户A'},\r\n        {id: 2, tenantName: '租户B'},\r\n        {id: 3, tenantName: '租户C'}\r\n      ];\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped></style>\r\n"]}]}